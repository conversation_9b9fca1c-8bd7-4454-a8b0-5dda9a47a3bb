const CPUAffinityManager = require('./cpu-affinity-manager');
const { fork } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

class ResearchBenchmarkRunner {
    constructor(config = {}) {
        this.config = {
            totalMessages: config.totalMessages || 1000,
            messageSizeKB: config.messageSizeKB || 1,
            producerInstances: config.producerInstances || 2,
            consumerInstances: config.consumerInstances || 2,
            testId: config.testId || `research_${new Date().toISOString().replace(/[:.]/g, '-')}`,
            cpuAffinityEnabled: config.cpuAffinityEnabled !== false,
            statisticalAnalysis: config.statisticalAnalysis !== false,
            ...config
        };

        this.cpuManager = new CPUAffinityManager();
        this.workers = {
            producers: [],
            consumers: []
        };
        this.metrics = {
            producers: [],
            consumers: [],
            system: [],
            latency: []
        };
        this.startTime = null;
        this.endTime = null;
    }

    /**
     * Initialize and run research-grade benchmark
     */
    async runBenchmark() {
        console.log('\n=== Research-Grade PGMQ Benchmark ===');
        console.log(`Test ID: ${this.config.testId}`);
        console.log(`CPU Affinity: ${this.config.cpuAffinityEnabled ? 'ENABLED' : 'DISABLED'}`);
        console.log(`Statistical Analysis: ${this.config.statisticalAnalysis ? 'ENABLED' : 'DISABLED'}`);
        
        try {
            // Generate CPU allocation strategy
            const allocation = this.cpuManager.generateCoreAllocation(
                this.config.producerInstances,
                this.config.consumerInstances
            );
            
            console.log(`\nCPU Allocation Strategy: ${allocation.strategy.toUpperCase()}`);
            console.log(`Total Cores Available: ${allocation.totalCores}`);
            console.log(`Core Utilization: ${allocation.utilization}%`);
            console.log(`Producer Cores: [${allocation.producerCores.join(', ')}]`);
            console.log(`Consumer Cores: [${allocation.consumerCores.join(', ')}]`);

            this.startTime = Date.now();

            // Start system monitoring
            this.startSystemMonitoring();

            // Create consumers with CPU affinity
            await this.createConsumersWithAffinity(allocation.consumerCores);
            
            // Wait for consumers to be ready
            await this.sleep(2000);

            // Create producers with CPU affinity
            await this.createProducersWithAffinity(allocation.producerCores);

            // Wait for completion
            await this.waitForCompletion();

            this.endTime = Date.now();

            // Collect and analyze results
            const results = await this.collectResults();
            
            if (this.config.statisticalAnalysis) {
                await this.performStatisticalAnalysis(results);
            }

            // Save research data
            await this.saveResearchData(results, allocation);

            console.log('\n=== Research Benchmark Complete ===');
            return results;

        } catch (error) {
            console.error('Research benchmark failed:', error);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    /**
     * Create consumers with CPU affinity
     */
    async createConsumersWithAffinity(consumerCores) {
        console.log('\n--- Creating Consumers with CPU Affinity ---');
        
        for (let i = 0; i < this.config.consumerInstances; i++) {
            const coreId = consumerCores[i];
            const workerEnv = {
                WORKER_ID: i,
                WORKER_TYPE: 'consumer',
                TEST_ID: this.config.testId,
                TARGET_MESSAGES: Math.floor(this.config.totalMessages / this.config.consumerInstances),
                MESSAGE_SIZE_KB: this.config.messageSizeKB,
                ASSIGNED_CORE: coreId
            };

            if (this.config.cpuAffinityEnabled) {
                const workerInfo = await this.cpuManager.createAffinityWorker(
                    path.join(__dirname, '../workers/research-consumer-worker.js'),
                    coreId,
                    workerEnv
                );
                
                this.workers.consumers.push(workerInfo);
                console.log(`Consumer-${i} created: PID ${workerInfo.pid}, Core ${coreId}, Affinity: ${workerInfo.affinityApplied}`);
            } else {
                const worker = fork(path.join(__dirname, '../workers/research-consumer-worker.js'), [], {
                    env: { ...process.env, ...workerEnv }
                });
                
                this.workers.consumers.push({
                    worker,
                    pid: worker.pid,
                    cores: [coreId],
                    affinityApplied: false
                });
                console.log(`Consumer-${i} created: PID ${worker.pid}, Core ${coreId} (no affinity)`);
            }
        }
    }

    /**
     * Create producers with CPU affinity
     */
    async createProducersWithAffinity(producerCores) {
        console.log('\n--- Creating Producers with CPU Affinity ---');
        
        for (let i = 0; i < this.config.producerInstances; i++) {
            const coreId = producerCores[i];
            const workerEnv = {
                WORKER_ID: i,
                WORKER_TYPE: 'producer',
                TEST_ID: this.config.testId,
                TARGET_MESSAGES: Math.floor(this.config.totalMessages / this.config.producerInstances),
                MESSAGE_SIZE_KB: this.config.messageSizeKB,
                ASSIGNED_CORE: coreId
            };

            if (this.config.cpuAffinityEnabled) {
                const workerInfo = await this.cpuManager.createAffinityWorker(
                    path.join(__dirname, '../workers/research-producer-worker.js'),
                    coreId,
                    workerEnv
                );
                
                this.workers.producers.push(workerInfo);
                console.log(`Producer-${i} created: PID ${workerInfo.pid}, Core ${coreId}, Affinity: ${workerInfo.affinityApplied}`);
            } else {
                const worker = fork(path.join(__dirname, '../workers/research-producer-worker.js'), [], {
                    env: { ...process.env, ...workerEnv }
                });
                
                this.workers.producers.push({
                    worker,
                    pid: worker.pid,
                    cores: [coreId],
                    affinityApplied: false
                });
                console.log(`Producer-${i} created: PID ${worker.pid}, Core ${coreId} (no affinity)`);
            }
        }
    }

    /**
     * Start system monitoring
     */
    startSystemMonitoring() {
        this.systemMonitorInterval = setInterval(async () => {
            const coreUsage = await this.cpuManager.getCoreUsage();
            const memUsage = process.memoryUsage();
            
            this.metrics.system.push({
                timestamp: Date.now(),
                cores: coreUsage.cores,
                memory: {
                    rss: memUsage.rss,
                    heapUsed: memUsage.heapUsed,
                    heapTotal: memUsage.heapTotal,
                    external: memUsage.external
                }
            });
        }, 1000);
    }

    /**
     * Wait for all workers to complete
     */
    async waitForCompletion() {
        console.log('\n--- Waiting for Benchmark Completion ---');
        
        const allWorkers = [
            ...this.workers.producers.map(w => w.worker),
            ...this.workers.consumers.map(w => w.worker)
        ];

        return new Promise((resolve) => {
            let completedWorkers = 0;
            const totalWorkers = allWorkers.length;

            allWorkers.forEach(worker => {
                worker.on('exit', () => {
                    completedWorkers++;
                    console.log(`Worker completed (${completedWorkers}/${totalWorkers})`);
                    
                    if (completedWorkers === totalWorkers) {
                        resolve();
                    }
                });

                worker.on('message', (message) => {
                    if (message.type === 'metrics') {
                        this.collectWorkerMetrics(message);
                    }
                });
            });
        });
    }

    /**
     * Collect worker metrics
     */
    collectWorkerMetrics(message) {
        if (message.workerType === 'producer') {
            this.metrics.producers.push(message.data);
        } else if (message.workerType === 'consumer') {
            this.metrics.consumers.push(message.data);
            if (message.data.latency) {
                this.metrics.latency.push(message.data.latency);
            }
        }
    }

    /**
     * Collect and analyze results
     */
    async collectResults() {
        const duration = this.endTime - this.startTime;
        
        // Calculate aggregate metrics
        const producerThroughput = this.metrics.producers.reduce((sum, p) => sum + (p.throughput || 0), 0);
        const consumerThroughput = this.metrics.consumers.reduce((sum, c) => sum + (c.throughput || 0), 0);
        
        const latencies = this.metrics.latency.filter(l => l && l > 0);
        const avgLatency = latencies.length > 0 ? latencies.reduce((sum, l) => sum + l, 0) / latencies.length : 0;

        return {
            testId: this.config.testId,
            duration,
            config: this.config,
            performance: {
                producerThroughput,
                consumerThroughput,
                avgLatency,
                latencies
            },
            workers: {
                producers: this.workers.producers.length,
                consumers: this.workers.consumers.length
            },
            cpuAllocation: this.cpuManager.getAllocationSummary(),
            rawMetrics: this.metrics
        };
    }

    /**
     * Perform statistical analysis
     */
    async performStatisticalAnalysis(results) {
        console.log('\n--- Statistical Analysis ---');
        
        const latencies = results.performance.latencies;
        if (latencies.length === 0) {
            console.log('No latency data available for statistical analysis');
            return;
        }

        // Calculate statistics
        const sorted = latencies.sort((a, b) => a - b);
        const mean = latencies.reduce((sum, l) => sum + l, 0) / latencies.length;
        const variance = latencies.reduce((sum, l) => sum + Math.pow(l - mean, 2), 0) / latencies.length;
        const stdDev = Math.sqrt(variance);
        
        const p50 = sorted[Math.floor(sorted.length * 0.5)];
        const p95 = sorted[Math.floor(sorted.length * 0.95)];
        const p99 = sorted[Math.floor(sorted.length * 0.99)];

        const stats = {
            count: latencies.length,
            mean: mean.toFixed(2),
            stdDev: stdDev.toFixed(2),
            min: Math.min(...latencies).toFixed(2),
            max: Math.max(...latencies).toFixed(2),
            p50: p50.toFixed(2),
            p95: p95.toFixed(2),
            p99: p99.toFixed(2),
            coefficientOfVariation: (stdDev / mean * 100).toFixed(2)
        };

        console.log(`Latency Statistics (ms):`);
        console.log(`  Count: ${stats.count}`);
        console.log(`  Mean: ${stats.mean}`);
        console.log(`  Std Dev: ${stats.stdDev}`);
        console.log(`  Min: ${stats.min}`);
        console.log(`  Max: ${stats.max}`);
        console.log(`  P50: ${stats.p50}`);
        console.log(`  P95: ${stats.p95}`);
        console.log(`  P99: ${stats.p99}`);
        console.log(`  CV: ${stats.coefficientOfVariation}%`);

        results.statistics = stats;
    }

    /**
     * Save research data
     */
    async saveResearchData(results, allocation) {
        const resultsDir = path.join(__dirname, '../shared-data/research-results');
        await fs.mkdir(resultsDir, { recursive: true });

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `research_${this.config.testId}_${timestamp}.json`;
        const filepath = path.join(resultsDir, filename);

        const researchData = {
            metadata: {
                testId: this.config.testId,
                timestamp: new Date().toISOString(),
                platform: os.platform(),
                nodeVersion: process.version,
                cpuModel: os.cpus()[0].model,
                totalMemory: os.totalmem(),
                benchmarkVersion: '2.0.0-research'
            },
            configuration: this.config,
            cpuAllocation: allocation,
            results,
            rawData: {
                systemMetrics: this.metrics.system,
                producerMetrics: this.metrics.producers,
                consumerMetrics: this.metrics.consumers
            }
        };

        await fs.writeFile(filepath, JSON.stringify(researchData, null, 2));
        console.log(`\nResearch data saved: ${filepath}`);
    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        if (this.systemMonitorInterval) {
            clearInterval(this.systemMonitorInterval);
        }

        // Clean up CPU affinity tracking
        [...this.workers.producers, ...this.workers.consumers].forEach(workerInfo => {
            if (workerInfo.pid) {
                this.cpuManager.removeAllocation(workerInfo.pid);
            }
        });
    }

    /**
     * Utility sleep function
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// CLI execution
if (require.main === module) {
    const config = {
        totalMessages: parseInt(process.env.TOTAL_MESSAGES) || 5000,
        messageSizeKB: parseInt(process.env.MESSAGE_SIZE_KB) || 1,
        producerInstances: parseInt(process.env.PRODUCER_INSTANCES) || 2,
        consumerInstances: parseInt(process.env.CONSUMER_INSTANCES) || 2,
        cpuAffinityEnabled: process.env.CPU_AFFINITY !== 'false',
        statisticalAnalysis: process.env.STATISTICAL_ANALYSIS !== 'false'
    };

    const runner = new ResearchBenchmarkRunner(config);
    runner.runBenchmark()
        .then(results => {
            console.log('\n✅ Research benchmark completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Research benchmark failed:', error);
            process.exit(1);
        });
}

module.exports = ResearchBenchmarkRunner;
