const fs = require('fs').promises;
const path = require('path');

class StatisticalAnalysis {
    constructor() {
        this.resultsDir = path.join(__dirname, '../shared-data/research-results');
    }

    /**
     * Calculate comprehensive statistics for a dataset
     */
    calculateStatistics(data) {
        if (!data || data.length === 0) {
            return null;
        }

        const sorted = [...data].sort((a, b) => a - b);
        const n = data.length;
        
        // Basic statistics
        const sum = data.reduce((acc, val) => acc + val, 0);
        const mean = sum / n;
        
        // Variance and standard deviation
        const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / n;
        const stdDev = Math.sqrt(variance);
        const sampleStdDev = n > 1 ? Math.sqrt(variance * n / (n - 1)) : 0;
        
        // Percentiles
        const percentiles = {
            p1: this.percentile(sorted, 0.01),
            p5: this.percentile(sorted, 0.05),
            p10: this.percentile(sorted, 0.10),
            p25: this.percentile(sorted, 0.25),
            p50: this.percentile(sorted, 0.50), // median
            p75: this.percentile(sorted, 0.75),
            p90: this.percentile(sorted, 0.90),
            p95: this.percentile(sorted, 0.95),
            p99: this.percentile(sorted, 0.99)
        };
        
        // Confidence intervals (95%)
        const standardError = sampleStdDev / Math.sqrt(n);
        const tValue = this.getTValue(n - 1, 0.05); // 95% confidence
        const marginOfError = tValue * standardError;
        
        const confidenceInterval = {
            lower: mean - marginOfError,
            upper: mean + marginOfError,
            marginOfError,
            confidenceLevel: 0.95
        };
        
        // Additional metrics
        const coefficientOfVariation = mean !== 0 ? (stdDev / mean) * 100 : 0;
        const skewness = this.calculateSkewness(data, mean, stdDev);
        const kurtosis = this.calculateKurtosis(data, mean, stdDev);
        
        return {
            count: n,
            sum,
            mean,
            median: percentiles.p50,
            mode: this.calculateMode(data),
            min: sorted[0],
            max: sorted[n - 1],
            range: sorted[n - 1] - sorted[0],
            variance,
            stdDev,
            sampleStdDev,
            standardError,
            coefficientOfVariation,
            skewness,
            kurtosis,
            percentiles,
            confidenceInterval,
            outliers: this.detectOutliers(sorted, percentiles.p25, percentiles.p75)
        };
    }

    /**
     * Calculate percentile value
     */
    percentile(sortedData, p) {
        const index = p * (sortedData.length - 1);
        const lower = Math.floor(index);
        const upper = Math.ceil(index);
        const weight = index % 1;
        
        if (upper >= sortedData.length) return sortedData[sortedData.length - 1];
        if (lower < 0) return sortedData[0];
        
        return sortedData[lower] * (1 - weight) + sortedData[upper] * weight;
    }

    /**
     * Get t-value for confidence interval calculation
     */
    getTValue(degreesOfFreedom, alpha) {
        // Simplified t-table for common cases
        const tTable = {
            1: { 0.05: 12.706 },
            2: { 0.05: 4.303 },
            3: { 0.05: 3.182 },
            4: { 0.05: 2.776 },
            5: { 0.05: 2.571 },
            10: { 0.05: 2.228 },
            20: { 0.05: 2.086 },
            30: { 0.05: 2.042 },
            50: { 0.05: 2.009 },
            100: { 0.05: 1.984 }
        };
        
        // Find closest degrees of freedom
        const df = Math.min(degreesOfFreedom, 100);
        const keys = Object.keys(tTable).map(Number).sort((a, b) => a - b);
        const closestDf = keys.reduce((prev, curr) => 
            Math.abs(curr - df) < Math.abs(prev - df) ? curr : prev
        );
        
        return tTable[closestDf][alpha] || 1.96; // fallback to z-value
    }

    /**
     * Calculate mode (most frequent value)
     */
    calculateMode(data) {
        const frequency = {};
        let maxFreq = 0;
        let modes = [];
        
        data.forEach(val => {
            frequency[val] = (frequency[val] || 0) + 1;
            if (frequency[val] > maxFreq) {
                maxFreq = frequency[val];
                modes = [val];
            } else if (frequency[val] === maxFreq && !modes.includes(val)) {
                modes.push(val);
            }
        });
        
        return modes.length === data.length ? null : modes;
    }

    /**
     * Calculate skewness
     */
    calculateSkewness(data, mean, stdDev) {
        if (stdDev === 0) return 0;
        
        const n = data.length;
        const skewSum = data.reduce((acc, val) => acc + Math.pow((val - mean) / stdDev, 3), 0);
        return (n / ((n - 1) * (n - 2))) * skewSum;
    }

    /**
     * Calculate kurtosis
     */
    calculateKurtosis(data, mean, stdDev) {
        if (stdDev === 0) return 0;
        
        const n = data.length;
        const kurtSum = data.reduce((acc, val) => acc + Math.pow((val - mean) / stdDev, 4), 0);
        const kurtosis = (n * (n + 1) / ((n - 1) * (n - 2) * (n - 3))) * kurtSum;
        const correction = 3 * Math.pow(n - 1, 2) / ((n - 2) * (n - 3));
        return kurtosis - correction; // Excess kurtosis
    }

    /**
     * Detect outliers using IQR method
     */
    detectOutliers(sortedData, q1, q3) {
        const iqr = q3 - q1;
        const lowerBound = q1 - 1.5 * iqr;
        const upperBound = q3 + 1.5 * iqr;
        
        return {
            mild: sortedData.filter(val => 
                (val < lowerBound && val >= q1 - 3 * iqr) || 
                (val > upperBound && val <= q3 + 3 * iqr)
            ),
            extreme: sortedData.filter(val => 
                val < q1 - 3 * iqr || val > q3 + 3 * iqr
            ),
            bounds: { lower: lowerBound, upper: upperBound }
        };
    }

    /**
     * Compare two datasets statistically
     */
    compareDatasets(dataset1, dataset2, name1 = 'Dataset 1', name2 = 'Dataset 2') {
        const stats1 = this.calculateStatistics(dataset1);
        const stats2 = this.calculateStatistics(dataset2);
        
        if (!stats1 || !stats2) {
            return { error: 'Invalid datasets for comparison' };
        }
        
        // Effect size (Cohen's d)
        const pooledStdDev = Math.sqrt(
            ((stats1.count - 1) * Math.pow(stats1.sampleStdDev, 2) + 
             (stats2.count - 1) * Math.pow(stats2.sampleStdDev, 2)) /
            (stats1.count + stats2.count - 2)
        );
        
        const cohensD = (stats1.mean - stats2.mean) / pooledStdDev;
        
        // Welch's t-test (unequal variances)
        const welchT = (stats1.mean - stats2.mean) / 
            Math.sqrt(Math.pow(stats1.standardError, 2) + Math.pow(stats2.standardError, 2));
        
        const welchDf = Math.pow(
            Math.pow(stats1.standardError, 2) + Math.pow(stats2.standardError, 2), 2
        ) / (
            Math.pow(stats1.standardError, 4) / (stats1.count - 1) +
            Math.pow(stats2.standardError, 4) / (stats2.count - 1)
        );
        
        return {
            [name1]: stats1,
            [name2]: stats2,
            comparison: {
                meanDifference: stats1.mean - stats2.mean,
                meanDifferencePercent: stats2.mean !== 0 ? 
                    ((stats1.mean - stats2.mean) / stats2.mean * 100) : null,
                cohensD,
                effectSize: this.interpretCohensD(cohensD),
                welchT,
                welchDf,
                significanceLevel: this.interpretTTest(Math.abs(welchT), welchDf)
            }
        };
    }

    /**
     * Interpret Cohen's d effect size
     */
    interpretCohensD(d) {
        const absD = Math.abs(d);
        if (absD < 0.2) return 'negligible';
        if (absD < 0.5) return 'small';
        if (absD < 0.8) return 'medium';
        return 'large';
    }

    /**
     * Interpret t-test results
     */
    interpretTTest(tValue, df) {
        // Simplified significance testing
        if (df >= 30) {
            if (tValue > 2.576) return 'p < 0.01 (highly significant)';
            if (tValue > 1.96) return 'p < 0.05 (significant)';
            if (tValue > 1.645) return 'p < 0.10 (marginally significant)';
        } else {
            // Conservative approach for small samples
            if (tValue > 3.0) return 'p < 0.01 (highly significant)';
            if (tValue > 2.0) return 'p < 0.05 (significant)';
            if (tValue > 1.5) return 'p < 0.10 (marginally significant)';
        }
        return 'not significant';
    }

    /**
     * Analyze research results from files
     */
    async analyzeResearchResults(testIds = null) {
        try {
            const files = await fs.readdir(this.resultsDir);
            const researchFiles = files.filter(f => f.startsWith('research_') && f.endsWith('.json'));
            
            if (researchFiles.length === 0) {
                throw new Error('No research result files found');
            }
            
            const analyses = [];
            
            for (const file of researchFiles) {
                const filePath = path.join(this.resultsDir, file);
                const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
                
                if (testIds && !testIds.includes(data.configuration.testId)) {
                    continue;
                }
                
                const analysis = this.analyzeTestResults(data);
                analyses.push(analysis);
            }
            
            return {
                totalTests: analyses.length,
                analyses,
                summary: this.generateSummary(analyses)
            };
            
        } catch (error) {
            throw new Error(`Failed to analyze research results: ${error.message}`);
        }
    }

    /**
     * Analyze individual test results
     */
    analyzeTestResults(testData) {
        const { results, configuration } = testData;
        
        // Extract latency data
        const latencies = results.performance.latencies || [];
        const latencyStats = this.calculateStatistics(latencies);
        
        // Extract throughput data from raw metrics
        const producerThroughputs = results.rawMetrics?.producers?.map(p => p.throughput).filter(t => t) || [];
        const consumerThroughputs = results.rawMetrics?.consumers?.map(c => c.throughput).filter(t => t) || [];
        
        const producerStats = this.calculateStatistics(producerThroughputs);
        const consumerStats = this.calculateStatistics(consumerThroughputs);
        
        return {
            testId: configuration.testId,
            configuration,
            statistics: {
                latency: latencyStats,
                producerThroughput: producerStats,
                consumerThroughput: consumerStats
            },
            performance: results.performance,
            cpuAllocation: results.cpuAllocation,
            timestamp: testData.metadata.timestamp
        };
    }

    /**
     * Generate summary across multiple tests
     */
    generateSummary(analyses) {
        if (analyses.length === 0) return null;
        
        const allLatencies = analyses.flatMap(a => a.performance.latencies || []);
        const allProducerThroughputs = analyses.map(a => a.performance.producerThroughput).filter(t => t);
        const allConsumerThroughputs = analyses.map(a => a.performance.consumerThroughput).filter(t => t);
        
        return {
            testCount: analyses.length,
            overallStatistics: {
                latency: this.calculateStatistics(allLatencies),
                producerThroughput: this.calculateStatistics(allProducerThroughputs),
                consumerThroughput: this.calculateStatistics(allConsumerThroughputs)
            },
            configurations: analyses.map(a => ({
                testId: a.testId,
                messages: a.configuration.totalMessages,
                producers: a.configuration.producerInstances,
                consumers: a.configuration.consumerInstances,
                cpuAffinity: a.configuration.cpuAffinityEnabled
            }))
        };
    }

    /**
     * Generate publication-ready report
     */
    async generatePublicationReport(testIds = null) {
        const analysis = await this.analyzeResearchResults(testIds);
        
        const report = {
            title: 'PostgreSQL PGMQ Performance Analysis Report',
            generatedAt: new Date().toISOString(),
            methodology: {
                description: 'Research-grade benchmarking with CPU affinity and statistical analysis',
                concurrentExecution: true,
                realTimeLatencyMeasurement: true,
                cpuAffinityEnabled: analysis.analyses.some(a => a.configuration.cpuAffinityEnabled)
            },
            summary: analysis.summary,
            detailedResults: analysis.analyses,
            statisticalSignificance: this.assessStatisticalSignificance(analysis.analyses),
            recommendations: this.generateRecommendations(analysis.summary)
        };
        
        // Save report
        const reportPath = path.join(this.resultsDir, `publication_report_${Date.now()}.json`);
        await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`Publication report saved: ${reportPath}`);
        return report;
    }

    /**
     * Assess statistical significance across tests
     */
    assessStatisticalSignificance(analyses) {
        if (analyses.length < 2) {
            return { message: 'Need at least 2 test results for significance testing' };
        }
        
        // Compare first and last test (or could be more sophisticated)
        const first = analyses[0];
        const last = analyses[analyses.length - 1];
        
        const latencyComparison = this.compareDatasets(
            first.performance.latencies || [],
            last.performance.latencies || [],
            `Test ${first.testId}`,
            `Test ${last.testId}`
        );
        
        return {
            latencyComparison,
            interpretation: 'Statistical comparison between first and last test results'
        };
    }

    /**
     * Generate performance recommendations
     */
    generateRecommendations(summary) {
        if (!summary) return [];
        
        const recommendations = [];
        
        // CPU affinity recommendation
        const cpuAffinityTests = summary.configurations.filter(c => c.cpuAffinity);
        if (cpuAffinityTests.length > 0 && cpuAffinityTests.length < summary.configurations.length) {
            recommendations.push({
                category: 'CPU Optimization',
                recommendation: 'Enable CPU affinity for consistent performance',
                evidence: 'Mixed results with and without CPU affinity detected'
            });
        }
        
        // Throughput optimization
        const latencyStats = summary.overallStatistics.latency;
        if (latencyStats && latencyStats.coefficientOfVariation > 50) {
            recommendations.push({
                category: 'Latency Optimization',
                recommendation: 'High latency variability detected - consider queue tuning',
                evidence: `CV: ${latencyStats.coefficientOfVariation.toFixed(1)}%`
            });
        }
        
        return recommendations;
    }
}

// CLI execution
if (require.main === module) {
    const analysis = new StatisticalAnalysis();
    
    const command = process.argv[2];
    const testIds = process.argv.slice(3);
    
    if (command === 'analyze') {
        analysis.analyzeResearchResults(testIds.length > 0 ? testIds : null)
            .then(results => {
                console.log(JSON.stringify(results, null, 2));
            })
            .catch(error => {
                console.error('Analysis failed:', error.message);
                process.exit(1);
            });
    } else if (command === 'report') {
        analysis.generatePublicationReport(testIds.length > 0 ? testIds : null)
            .then(report => {
                console.log('Publication report generated successfully');
                console.log(`Tests analyzed: ${report.summary?.testCount || 0}`);
            })
            .catch(error => {
                console.error('Report generation failed:', error.message);
                process.exit(1);
            });
    } else {
        console.log('Usage:');
        console.log('  node statistical-analysis.js analyze [testId1] [testId2] ...');
        console.log('  node statistical-analysis.js report [testId1] [testId2] ...');
    }
}

module.exports = StatisticalAnalysis;
