<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PGMQ CPU Configuration Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .system-info {
            grid-column: 1 / -1;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .info-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .info-card .value {
            font-size: 1.8em;
            font-weight: bold;
            color: #3498db;
        }

        .info-card .unit {
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .cpu-cores {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .cpu-core {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .cpu-core.available {
            background: #95a5a6;
        }

        .cpu-core.producer {
            background: #3498db;
        }

        .cpu-core.consumer {
            background: #e74c3c;
        }

        .cpu-core.shared {
            background: linear-gradient(45deg, #3498db 50%, #e74c3c 50%);
        }

        .cpu-core:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .legend {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .status {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .preset-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .preset-btn {
            background: white;
            border: 2px solid #e9ecef;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: left;
        }

        .preset-btn:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .preset-btn.active {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .preset-btn h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .preset-btn p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .real-time-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .preset-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 PGMQ CPU Configuration Interface</h1>
            <p>Research-Grade PostgreSQL PGMQ Performance Configuration</p>
        </div>

        <div class="main-content">
            <!-- System Information -->
            <div class="section system-info">
                <h2>📊 System Information</h2>
                <div class="info-grid" id="systemInfo">
                    <!-- System info will be populated here -->
                </div>
            </div>

            <!-- CPU Core Allocation -->
            <div class="section">
                <h2>🖥️ CPU Core Allocation</h2>
                <div id="cpuCores" class="cpu-cores">
                    <!-- CPU cores will be populated here -->
                </div>
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #95a5a6;"></div>
                        <span>Available</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #3498db;"></div>
                        <span>Producer</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #e74c3c;"></div>
                        <span>Consumer</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(45deg, #3498db 50%, #e74c3c 50%);"></div>
                        <span>Shared</span>
                    </div>
                </div>
            </div>

            <!-- Configuration Presets -->
            <div class="section">
                <h2>⚙️ Configuration Presets</h2>
                <div class="preset-buttons" id="presetButtons">
                    <!-- Presets will be populated here -->
                </div>
            </div>

            <!-- Benchmark Configuration -->
            <div class="section">
                <h2>🚀 Benchmark Configuration</h2>
                <form id="benchmarkForm">
                    <div class="form-group">
                        <label for="totalMessages">Total Messages:</label>
                        <input type="number" id="totalMessages" value="5000" min="100" max="1000000">
                    </div>
                    
                    <div class="form-group">
                        <label for="messageSizeKB">Message Size (KB):</label>
                        <input type="number" id="messageSizeKB" value="1" min="1" max="1024">
                    </div>
                    
                    <div class="form-group">
                        <label for="producerInstances">Producer Instances:</label>
                        <input type="number" id="producerInstances" value="2" min="1" max="16">
                    </div>
                    
                    <div class="form-group">
                        <label for="consumerInstances">Consumer Instances:</label>
                        <input type="number" id="consumerInstances" value="2" min="1" max="16">
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="cpuAffinityEnabled" checked>
                            <label for="cpuAffinityEnabled">Enable CPU Affinity</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="statisticalAnalysis" checked>
                            <label for="statisticalAnalysis">Enable Statistical Analysis</label>
                        </div>
                    </div>
                </form>
                
                <button class="btn" onclick="generateAllocation()">🔄 Generate Allocation</button>
                <button class="btn btn-success" onclick="startBenchmark()">▶️ Start Benchmark</button>
                <button class="btn btn-warning" onclick="testAffinity()">🧪 Test CPU Affinity</button>
            </div>

            <!-- Real-time Monitoring -->
            <div class="section">
                <h2>📈 Real-time Monitoring</h2>
                <div id="benchmarkStatus" class="status info">
                    Ready to start benchmark
                </div>
                <div class="real-time-stats" id="realTimeStats">
                    <!-- Real-time stats will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let systemInfo = {};
        let currentAllocation = {};
        let presets = [];
        let benchmarkRunning = false;

        // Initialize the interface
        async function init() {
            await loadSystemInfo();
            await loadPresets();
            await loadCurrentConfig();
            startRealTimeMonitoring();
        }

        // Load system information
        async function loadSystemInfo() {
            try {
                const response = await fetch('/api/system-info');
                systemInfo = await response.json();
                displaySystemInfo();
                displayCPUCores();
            } catch (error) {
                console.error('Failed to load system info:', error);
            }
        }

        // Display system information
        function displaySystemInfo() {
            const container = document.getElementById('systemInfo');
            container.innerHTML = `
                <div class="info-card">
                    <h3>Platform</h3>
                    <div class="value">${systemInfo.platform}</div>
                    <div class="unit">${systemInfo.arch}</div>
                </div>
                <div class="info-card">
                    <h3>CPU Model</h3>
                    <div class="value">${systemInfo.cpuModel?.split(' ')[0] || 'Unknown'}</div>
                    <div class="unit">${systemInfo.totalCores} cores</div>
                </div>
                <div class="info-card">
                    <h3>Total Memory</h3>
                    <div class="value">${(systemInfo.memory.total / 1024 / 1024 / 1024).toFixed(1)}</div>
                    <div class="unit">GB</div>
                </div>
                <div class="info-card">
                    <h3>Memory Usage</h3>
                    <div class="value">${systemInfo.memory.usage}</div>
                    <div class="unit">%</div>
                </div>
                <div class="info-card">
                    <h3>CPU Affinity</h3>
                    <div class="value">${systemInfo.affinitySupported ? '✅' : '❌'}</div>
                    <div class="unit">${systemInfo.affinitySupported ? 'Supported' : 'Not Supported'}</div>
                </div>
                <div class="info-card">
                    <h3>Node.js</h3>
                    <div class="value">${systemInfo.nodeVersion}</div>
                    <div class="unit">Runtime</div>
                </div>
            `;
        }

        // Display CPU cores
        function displayCPUCores() {
            const container = document.getElementById('cpuCores');
            container.innerHTML = '';

            for (let i = 0; i < systemInfo.totalCores; i++) {
                const core = document.createElement('div');
                core.className = 'cpu-core available';
                core.textContent = i;
                core.title = `Core ${i} - Click to toggle assignment`;
                core.onclick = () => toggleCoreAssignment(i);
                container.appendChild(core);
            }
        }

        // Load configuration presets
        async function loadPresets() {
            try {
                const response = await fetch('/api/configurations');
                const data = await response.json();
                presets = data.presets;
                displayPresets();
            } catch (error) {
                console.error('Failed to load presets:', error);
            }
        }

        // Display configuration presets
        function displayPresets() {
            const container = document.getElementById('presetButtons');
            container.innerHTML = '';

            presets.forEach((preset, index) => {
                const button = document.createElement('div');
                button.className = 'preset-btn';
                button.onclick = () => selectPreset(index);
                button.innerHTML = `
                    <h4>${preset.name}</h4>
                    <p>${preset.description}</p>
                `;
                container.appendChild(button);
            });
        }

        // Select a preset configuration
        function selectPreset(index) {
            const preset = presets[index];
            const config = preset.config;

            // Update form fields
            document.getElementById('totalMessages').value = config.totalMessages;
            document.getElementById('producerInstances').value = config.producerInstances;
            document.getElementById('consumerInstances').value = config.consumerInstances;
            document.getElementById('cpuAffinityEnabled').checked = config.cpuAffinityEnabled;

            // Update preset button styling
            document.querySelectorAll('.preset-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.preset-btn')[index].classList.add('active');

            // Generate allocation
            generateAllocation();
        }

        // Generate CPU allocation
        async function generateAllocation() {
            const producerInstances = parseInt(document.getElementById('producerInstances').value);
            const consumerInstances = parseInt(document.getElementById('consumerInstances').value);

            try {
                const response = await fetch('/api/generate-allocation', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ producerInstances, consumerInstances })
                });

                currentAllocation = await response.json();
                updateCoreDisplay();
            } catch (error) {
                console.error('Failed to generate allocation:', error);
            }
        }

        // Update CPU core display with allocation
        function updateCoreDisplay() {
            const cores = document.querySelectorAll('.cpu-core');

            cores.forEach((core, index) => {
                core.className = 'cpu-core available';

                if (currentAllocation.producerCores?.includes(index)) {
                    if (currentAllocation.consumerCores?.includes(index)) {
                        core.className = 'cpu-core shared';
                    } else {
                        core.className = 'cpu-core producer';
                    }
                } else if (currentAllocation.consumerCores?.includes(index)) {
                    core.className = 'cpu-core consumer';
                }
            });
        }

        // Toggle core assignment (manual mode)
        function toggleCoreAssignment(coreId) {
            // This could be implemented for manual core assignment
            console.log(`Toggle core ${coreId}`);
        }

        // Start benchmark
        async function startBenchmark() {
            if (benchmarkRunning) {
                alert('Benchmark is already running!');
                return;
            }

            const config = {
                testId: `research_${Date.now()}`,
                totalMessages: parseInt(document.getElementById('totalMessages').value),
                messageSizeKB: parseInt(document.getElementById('messageSizeKB').value),
                producerInstances: parseInt(document.getElementById('producerInstances').value),
                consumerInstances: parseInt(document.getElementById('consumerInstances').value),
                cpuAffinityEnabled: document.getElementById('cpuAffinityEnabled').checked,
                statisticalAnalysis: document.getElementById('statisticalAnalysis').checked
            };

            try {
                const response = await fetch('/api/start-benchmark', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });

                const result = await response.json();

                if (result.success) {
                    benchmarkRunning = true;
                    updateBenchmarkStatus(`Benchmark started: ${result.testId}`, 'success');
                } else {
                    updateBenchmarkStatus(`Failed to start benchmark: ${result.error}`, 'error');
                }
            } catch (error) {
                updateBenchmarkStatus(`Error starting benchmark: ${error.message}`, 'error');
            }
        }

        // Test CPU affinity
        async function testAffinity() {
            const coreId = 0; // Test with core 0

            try {
                const response = await fetch('/api/test-affinity', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ coreId })
                });

                const result = await response.json();

                if (result.success) {
                    updateBenchmarkStatus(`CPU Affinity test successful on core ${coreId}`, 'success');
                } else {
                    updateBenchmarkStatus(`CPU Affinity test failed: ${result.error || 'Not supported'}`, 'error');
                }
            } catch (error) {
                updateBenchmarkStatus(`CPU Affinity test error: ${error.message}`, 'error');
            }
        }

        // Update benchmark status
        function updateBenchmarkStatus(message, type) {
            const statusElement = document.getElementById('benchmarkStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // Load current configuration
        async function loadCurrentConfig() {
            try {
                const response = await fetch('/api/cpu-config');
                const config = await response.json();

                // Apply configuration to form
                if (config.benchmark) {
                    document.getElementById('totalMessages').value = config.benchmark.defaultMessages;
                    document.getElementById('producerInstances').value = config.benchmark.defaultProducers;
                    document.getElementById('consumerInstances').value = config.benchmark.defaultConsumers;
                    document.getElementById('cpuAffinityEnabled').checked = config.cpuAffinity?.enabled !== false;
                    document.getElementById('statisticalAnalysis').checked = config.benchmark.statisticalAnalysis !== false;
                }
            } catch (error) {
                console.error('Failed to load current config:', error);
            }
        }

        // Start real-time monitoring
        function startRealTimeMonitoring() {
            setInterval(async () => {
                await updateRealTimeStats();
                await checkBenchmarkStatus();
            }, 2000);
        }

        // Update real-time statistics
        async function updateRealTimeStats() {
            try {
                const response = await fetch('/api/cpu-usage');
                const data = await response.json();

                const container = document.getElementById('realTimeStats');

                if (data.usage && data.usage.cores) {
                    const avgCpuUsage = data.usage.cores.reduce((sum, core) => sum + core.usage, 0) / data.usage.cores.length;

                    container.innerHTML = `
                        <div class="stat-item">
                            <div class="stat-value">${avgCpuUsage.toFixed(1)}%</div>
                            <div class="stat-label">Avg CPU Usage</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${data.allocation?.activeAllocations || 0}</div>
                            <div class="stat-label">Active Workers</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${data.allocation?.totalCores || systemInfo.totalCores}</div>
                            <div class="stat-label">Total Cores</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${data.allocation?.affinitySupported ? '✅' : '❌'}</div>
                            <div class="stat-label">Affinity Status</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Failed to update real-time stats:', error);
            }
        }

        // Check benchmark status
        async function checkBenchmarkStatus() {
            try {
                const response = await fetch('/api/benchmark-status');
                const status = await response.json();

                if (benchmarkRunning && !status.running) {
                    benchmarkRunning = false;
                    updateBenchmarkStatus('Benchmark completed', 'success');
                }
            } catch (error) {
                console.error('Failed to check benchmark status:', error);
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
