const { spawn, exec } = require('child_process');
const os = require('os');
const cluster = require('cluster');

class CPUAffinityManager {
    constructor() {
        this.totalCores = os.cpus().length;
        this.affinitySupported = this.checkAffinitySupport();
        this.coreAllocations = new Map();
    }

    /**
     * Check if CPU affinity is supported on this system
     */
    checkAffinitySupport() {
        const platform = os.platform();
        return platform === 'linux' || platform === 'darwin';
    }

    /**
     * Generate CPU core allocation strategy
     */
    generateCoreAllocation(producerInstances, consumerInstances) {
        const totalWorkers = producerInstances + consumerInstances;
        
        if (totalWorkers <= this.totalCores) {
            // Dedicated core strategy
            const producerCores = Array.from({length: producerInstances}, (_, i) => i);
            const consumerCores = Array.from({length: consumerInstances}, (_, i) => i + producerInstances);
            
            return {
                strategy: 'dedicated',
                producerCores,
                consumerCores,
                totalCores: this.totalCores,
                utilization: (totalWorkers / this.totalCores * 100).toFixed(1)
            };
        } else {
            // Shared core strategy with round-robin
            const producerCores = Array.from({length: producerInstances}, (_, i) => i % this.totalCores);
            const consumerCores = Array.from({length: consumerInstances}, (_, i) => (i + producerInstances) % this.totalCores);
            
            return {
                strategy: 'shared',
                producerCores,
                consumerCores,
                totalCores: this.totalCores,
                utilization: '100.0'
            };
        }
    }

    /**
     * Apply CPU affinity to a process using taskset (Linux) or cpuset (macOS)
     */
    async applyCPUAffinity(pid, coreIds) {
        if (!this.affinitySupported) {
            console.warn(`[CPUAffinity] CPU affinity not supported on ${os.platform()}`);
            return false;
        }

        try {
            const coreList = Array.isArray(coreIds) ? coreIds.join(',') : coreIds.toString();
            
            if (os.platform() === 'linux') {
                // Use taskset on Linux
                await this.execCommand(`taskset -cp ${coreList} ${pid}`);
                console.log(`[CPUAffinity] Applied Linux taskset: PID ${pid} -> cores ${coreList}`);
            } else if (os.platform() === 'darwin') {
                // Use thread affinity on macOS (limited support)
                console.log(`[CPUAffinity] macOS detected: PID ${pid} suggested cores ${coreList}`);
                // Note: macOS doesn't have direct CPU affinity like Linux
            }
            
            this.coreAllocations.set(pid, coreIds);
            return true;
        } catch (error) {
            console.error(`[CPUAffinity] Failed to apply CPU affinity: ${error.message}`);
            return false;
        }
    }

    /**
     * Execute system command with promise
     */
    execCommand(command) {
        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(stdout);
                }
            });
        });
    }

    /**
     * Create worker with CPU affinity using Node.js cluster
     */
    createAffinityWorker(workerScript, coreIds, env = {}) {
        return new Promise((resolve, reject) => {
            if (cluster.isMaster || cluster.isPrimary) {
                // Set up cluster environment
                cluster.setupMaster({
                    exec: workerScript,
                    silent: false
                });

                const worker = cluster.fork({
                    ...process.env,
                    ...env,
                    ASSIGNED_CORES: Array.isArray(coreIds) ? coreIds.join(',') : coreIds.toString()
                });

                worker.on('online', async () => {
                    // Apply CPU affinity once worker is online
                    const success = await this.applyCPUAffinity(worker.process.pid, coreIds);
                    resolve({
                        worker,
                        pid: worker.process.pid,
                        cores: coreIds,
                        affinityApplied: success
                    });
                });

                worker.on('error', reject);
            } else {
                reject(new Error('createAffinityWorker must be called from master process'));
            }
        });
    }

    /**
     * Get current CPU affinity for a process
     */
    async getCurrentAffinity(pid) {
        if (!this.affinitySupported || os.platform() !== 'linux') {
            return null;
        }

        try {
            const output = await this.execCommand(`taskset -cp ${pid}`);
            // Parse output like "pid 1234's current affinity list: 0,1,2,3"
            const match = output.match(/affinity list: (.+)/);
            return match ? match[1].split(',').map(c => parseInt(c.trim())) : null;
        } catch (error) {
            console.error(`[CPUAffinity] Failed to get current affinity: ${error.message}`);
            return null;
        }
    }

    /**
     * Monitor CPU usage per core
     */
    async getCoreUsage() {
        const cpus = os.cpus();
        const usage = cpus.map((cpu, index) => {
            const total = Object.values(cpu.times).reduce((acc, time) => acc + time, 0);
            const idle = cpu.times.idle;
            const used = total - idle;
            const percentage = total > 0 ? (used / total * 100).toFixed(1) : 0;
            
            return {
                core: index,
                model: cpu.model,
                speed: cpu.speed,
                usage: parseFloat(percentage),
                times: cpu.times
            };
        });

        return {
            totalCores: this.totalCores,
            cores: usage,
            timestamp: Date.now()
        };
    }

    /**
     * Get allocation summary
     */
    getAllocationSummary() {
        const allocations = Array.from(this.coreAllocations.entries()).map(([pid, cores]) => ({
            pid,
            cores: Array.isArray(cores) ? cores : [cores]
        }));

        return {
            totalCores: this.totalCores,
            affinitySupported: this.affinitySupported,
            platform: os.platform(),
            activeAllocations: allocations.length,
            allocations
        };
    }

    /**
     * Clean up worker affinity tracking
     */
    removeAllocation(pid) {
        return this.coreAllocations.delete(pid);
    }
}

module.exports = CPUAffinityManager;
