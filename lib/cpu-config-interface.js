const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');
const CPUAffinityManager = require('./cpu-affinity-manager');
const ResearchBenchmarkRunner = require('./research-benchmark-runner');

class CPUConfigInterface {
    constructor(port = 3002) {
        this.app = express();
        this.port = port;
        this.cpuManager = new CPUAffinityManager();
        this.currentBenchmark = null;
        this.configFile = path.join(__dirname, '../config/cpu-config.json');
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.static(path.join(__dirname, '../dashboard/public')));
        this.app.use('/cpu-config', express.static(path.join(__dirname, 'cpu-config-ui')));
    }

    setupRoutes() {
        // Serve CPU configuration interface
        this.app.get('/cpu-config', (req, res) => {
            res.sendFile(path.join(__dirname, 'cpu-config-ui', 'index.html'));
        });

        // Get system information
        this.app.get('/api/system-info', async (req, res) => {
            try {
                const cpuInfo = os.cpus();
                const coreUsage = await this.cpuManager.getCoreUsage();
                const memInfo = {
                    total: os.totalmem(),
                    free: os.freemem(),
                    used: os.totalmem() - os.freemem(),
                    usage: ((os.totalmem() - os.freemem()) / os.totalmem() * 100).toFixed(1)
                };

                res.json({
                    platform: os.platform(),
                    arch: os.arch(),
                    nodeVersion: process.version,
                    cpuModel: cpuInfo[0].model,
                    totalCores: cpuInfo.length,
                    cores: coreUsage.cores,
                    memory: memInfo,
                    affinitySupported: this.cpuManager.affinitySupported
                });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // Get current CPU configuration
        this.app.get('/api/cpu-config', async (req, res) => {
            try {
                const config = await this.loadConfig();
                res.json(config);
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // Save CPU configuration
        this.app.post('/api/cpu-config', async (req, res) => {
            try {
                const config = req.body;
                await this.saveConfig(config);
                res.json({ success: true, config });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // Generate CPU allocation strategy
        this.app.post('/api/generate-allocation', (req, res) => {
            try {
                const { producerInstances, consumerInstances } = req.body;
                const allocation = this.cpuManager.generateCoreAllocation(
                    producerInstances, 
                    consumerInstances
                );
                res.json(allocation);
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // Start benchmark with configuration
        this.app.post('/api/start-benchmark', async (req, res) => {
            try {
                if (this.currentBenchmark) {
                    return res.status(400).json({ error: 'Benchmark already running' });
                }

                const config = req.body;
                this.currentBenchmark = new ResearchBenchmarkRunner(config);
                
                // Run benchmark in background
                this.currentBenchmark.runBenchmark()
                    .then(results => {
                        console.log('Benchmark completed:', results.testId);
                        this.currentBenchmark = null;
                    })
                    .catch(error => {
                        console.error('Benchmark failed:', error);
                        this.currentBenchmark = null;
                    });

                res.json({ 
                    success: true, 
                    testId: config.testId,
                    message: 'Benchmark started' 
                });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // Get benchmark status
        this.app.get('/api/benchmark-status', (req, res) => {
            res.json({
                running: !!this.currentBenchmark,
                testId: this.currentBenchmark?.config?.testId || null
            });
        });

        // Get real-time CPU usage
        this.app.get('/api/cpu-usage', async (req, res) => {
            try {
                const usage = await this.cpuManager.getCoreUsage();
                const allocation = this.cpuManager.getAllocationSummary();
                res.json({ usage, allocation });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // Get available configurations
        this.app.get('/api/configurations', async (req, res) => {
            try {
                const configs = await this.getAvailableConfigurations();
                res.json(configs);
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // Test CPU affinity
        this.app.post('/api/test-affinity', async (req, res) => {
            try {
                const { coreId } = req.body;
                const testPid = process.pid;
                const success = await this.cpuManager.applyCPUAffinity(testPid, [coreId]);
                
                // Get current affinity to verify
                const currentAffinity = await this.cpuManager.getCurrentAffinity(testPid);
                
                res.json({
                    success,
                    testPid,
                    requestedCore: coreId,
                    currentAffinity,
                    platform: os.platform(),
                    supported: this.cpuManager.affinitySupported
                });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
    }

    async loadConfig() {
        try {
            const data = await fs.readFile(this.configFile, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            // Return default configuration
            return this.getDefaultConfig();
        }
    }

    async saveConfig(config) {
        const configDir = path.dirname(this.configFile);
        await fs.mkdir(configDir, { recursive: true });
        await fs.writeFile(this.configFile, JSON.stringify(config, null, 2));
    }

    getDefaultConfig() {
        const totalCores = os.cpus().length;
        return {
            version: '1.0.0',
            lastUpdated: new Date().toISOString(),
            cpuAffinity: {
                enabled: true,
                strategy: 'dedicated', // 'dedicated' or 'shared'
                producerCores: Array.from({length: Math.floor(totalCores / 2)}, (_, i) => i),
                consumerCores: Array.from({length: Math.floor(totalCores / 2)}, (_, i) => i + Math.floor(totalCores / 2))
            },
            memory: {
                maxHeapSize: '2048m',
                maxOldSpace: '1536m',
                workerMemoryLimit: '512m'
            },
            benchmark: {
                defaultMessages: 5000,
                defaultMessageSize: 1,
                defaultProducers: 2,
                defaultConsumers: 2,
                statisticalAnalysis: true
            },
            monitoring: {
                systemMetricsInterval: 1000,
                workerMetricsInterval: 500,
                enableDetailedLogging: true
            }
        };
    }

    async getAvailableConfigurations() {
        const totalCores = os.cpus().length;
        
        return {
            presets: [
                {
                    name: 'Single Core Test',
                    description: 'Minimal configuration for testing',
                    config: {
                        totalMessages: 1000,
                        producerInstances: 1,
                        consumerInstances: 1,
                        cpuAffinityEnabled: true
                    }
                },
                {
                    name: 'Balanced Load',
                    description: 'Equal producers and consumers',
                    config: {
                        totalMessages: 5000,
                        producerInstances: Math.floor(totalCores / 4) || 1,
                        consumerInstances: Math.floor(totalCores / 4) || 1,
                        cpuAffinityEnabled: true
                    }
                },
                {
                    name: 'Producer Heavy',
                    description: 'More producers than consumers',
                    config: {
                        totalMessages: 10000,
                        producerInstances: Math.floor(totalCores * 0.6) || 2,
                        consumerInstances: Math.floor(totalCores * 0.3) || 1,
                        cpuAffinityEnabled: true
                    }
                },
                {
                    name: 'Consumer Heavy',
                    description: 'More consumers than producers',
                    config: {
                        totalMessages: 10000,
                        producerInstances: Math.floor(totalCores * 0.3) || 1,
                        consumerInstances: Math.floor(totalCores * 0.6) || 2,
                        cpuAffinityEnabled: true
                    }
                },
                {
                    name: 'Maximum Throughput',
                    description: 'Use all available cores',
                    config: {
                        totalMessages: 20000,
                        producerInstances: Math.floor(totalCores / 2),
                        consumerInstances: Math.floor(totalCores / 2),
                        cpuAffinityEnabled: true
                    }
                }
            ],
            systemLimits: {
                maxCores: totalCores,
                maxMemoryMB: Math.floor(os.totalmem() / 1024 / 1024),
                recommendedMaxWorkers: totalCores,
                platformSupport: {
                    cpuAffinity: this.cpuManager.affinitySupported,
                    platform: os.platform()
                }
            }
        };
    }

    start() {
        return new Promise((resolve) => {
            this.server = this.app.listen(this.port, () => {
                console.log(`\n🔧 CPU Configuration Interface running on http://localhost:${this.port}/cpu-config`);
                console.log(`📊 System Info API: http://localhost:${this.port}/api/system-info`);
                console.log(`⚙️  Configuration API: http://localhost:${this.port}/api/cpu-config`);
                resolve();
            });
        });
    }

    stop() {
        if (this.server) {
            this.server.close();
        }
    }
}

// CLI execution
if (require.main === module) {
    const port = parseInt(process.env.CPU_CONFIG_PORT) || 3002;
    const interface = new CPUConfigInterface(port);
    
    interface.start()
        .then(() => {
            console.log('\n✅ CPU Configuration Interface started successfully');
            console.log('Press Ctrl+C to stop');
        })
        .catch(error => {
            console.error('❌ Failed to start CPU Configuration Interface:', error);
            process.exit(1);
        });

    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down CPU Configuration Interface...');
        interface.stop();
        process.exit(0);
    });
}

module.exports = CPUConfigInterface;
