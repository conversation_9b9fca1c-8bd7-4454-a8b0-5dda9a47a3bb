# PM2 Production Deployment

⚠️ **IMPORTANT: This directory contains PM2 configuration files for PRODUCTION deployment only.**

## Purpose

These PM2 ecosystem files are designed for:
- **Production environment deployment**
- **Long-running message queue services**
- **Independent producer/consumer clusters**

## NOT for Research Benchmarking

❌ **DO NOT USE** these files for research or benchmarking purposes because:
- They run producers and consumers independently
- No coordinated measurement or latency tracking
- Missing real-time metrics collection
- No queue depth monitoring integration

## For Research Use Instead

✅ **Use the main benchmark runner:**
```bash
npm run research-benchmark    # Research-grade benchmarking
npm run benchmark            # Basic benchmarking
npm run test-concurrent      # Concurrent testing
npm run dashboard           # Real-time monitoring
```

## Production Deployment Usage

If you need to deploy PGMQ producers/consumers in production:

```bash
# Start producers
pm2 start deployment/pm2/ecosystem.producer.config.js

# Start consumers  
pm2 start deployment/pm2/ecosystem.consumer.config.js

# Monitor
pm2 status
pm2 logs

# Stop
pm2 stop all
```

## Configuration

Edit the ecosystem files in this directory to match your production environment:
- Database connection strings
- Worker instance counts
- Resource limits
- Environment variables
