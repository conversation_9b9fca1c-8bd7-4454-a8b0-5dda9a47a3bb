module.exports = {
  apps: [
    {
      name: 'pgmq-consumer',
      script: './workers/consumer-worker.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        DB_HOST: 'localhost',
        DB_PORT: '5433',
        DB_NAME: 'postgres',
        DB_USER: 'postgres',
        DB_PASSWORD: 'postgres',
        QUEUE_NAME: 'pgmq_benchmark',
        TEST_ID: 'manual_test',
        TOTAL_MESSAGES: 10000,
        TIMEOUT_MS: 300000,
        WORKER_ID: 'pm2'
      },
      env_production: {
        DB_HOST: 'localhost',
        DB_PORT: '5432',
        TOTAL_MESSAGES: 20000,
        TIMEOUT_MS: 600000
      },
      max_memory_restart: '500M',
      error_file: './shared-data/logs/consumer-error.log',
      out_file: './shared-data/logs/consumer-out.log',
      log_file: './shared-data/logs/consumer-combined.log',
      time: true
    }
  ]
};
