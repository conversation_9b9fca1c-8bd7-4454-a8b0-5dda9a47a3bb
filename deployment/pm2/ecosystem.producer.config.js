module.exports = {
  apps: [
    {
      name: 'pgmq-producer',
      script: './workers/producer-worker.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        DB_HOST: 'localhost',
        DB_PORT: '5433',
        DB_NAME: 'postgres',
        DB_USER: 'postgres',
        DB_PASSWORD: 'postgres',
        QUEUE_NAME: 'pgmq_benchmark',
        TEST_ID: 'manual_test',
        TOTAL_MESSAGES: 5000,
        MESSAGE_SIZE_KB: 1,
        WORKER_ID: 'pm2'
      },
      env_production: {
        DB_HOST: 'localhost',
        DB_PORT: '5432',
        TOTAL_MESSAGES: 10000,
        MESSAGE_SIZE_KB: 10
      },
      max_memory_restart: '500M',
      error_file: './shared-data/logs/producer-error.log',
      out_file: './shared-data/logs/producer-out.log',
      log_file: './shared-data/logs/producer-combined.log',
      time: true
    }
  ]
};
