{"name": "pgmq-benchmark", "version": "1.0.0", "description": "PGMQ Performance Benchmarking System", "main": "benchmark-runner.js", "scripts": {"start": "node benchmark-runner.js", "setup": "node setup.js", "demo": "node demo.js", "test-concurrent": "node test-concurrent-benchmark.js", "producer": "pm2 start ecosystem.producer.config.js", "consumer": "pm2 start ecosystem.consumer.config.js", "dashboard": "node dashboard/server.js", "benchmark": "node benchmark-runner.js", "stress-test": "node stress-test.js", "stop": "pm2 stop all", "restart": "pm2 restart all", "status": "pm2 status", "logs": "pm2 logs", "clean": "pm2 delete all"}, "dependencies": {"chart.js": "^4.4.0", "csv-parser": "^3.0.0", "express": "^4.18.2", "csv-writer": "^1.6.0", "pg-boss": "^10.2.0", "pg": "^8.15.6", "pgmq-js": "^1.3.0", "ws": "^8.14.2", "systeminformation": "^5.21.20"}, "devDependencies": {"pm2": "^5.3.0"}}