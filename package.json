{"name": "pgmq-benchmark", "version": "1.0.0", "description": "PGMQ Performance Benchmarking System", "main": "benchmark-runner.js", "scripts": {"start": "node benchmark-runner.js", "setup": "node setup.js", "demo": "node demo.js", "test-concurrent": "node test-concurrent-benchmark.js", "dashboard": "node dashboard/server.js", "benchmark": "node benchmark-runner.js", "stress-test": "node stress-test.js", "research-benchmark": "node lib/research-benchmark-runner.js", "cpu-config": "node lib/cpu-config-interface.js", "statistical-analysis": "node lib/statistical-analysis.js"}, "dependencies": {"chart.js": "^4.4.0", "csv-parser": "^3.0.0", "express": "^4.18.2", "csv-writer": "^1.6.0", "pg-boss": "^10.2.0", "pg": "^8.15.6", "pgmq-js": "^1.3.0", "ws": "^8.14.2", "systeminformation": "^5.21.20"}, "devDependencies": {"pm2": "^5.3.0"}}