const { Client } = require('pg');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

class ResearchConsumerWorker {
    constructor() {
        this.workerId = process.env.WORKER_ID || '0';
        this.testId = process.env.TEST_ID || 'research_test';
        this.targetMessages = parseInt(process.env.TARGET_MESSAGES) || 1000;
        this.messageSizeKB = parseInt(process.env.MESSAGE_SIZE_KB) || 1;
        this.assignedCore = process.env.ASSIGNED_CORE;
        
        this.client = null;
        this.processedMessages = 0;
        this.startTime = null;
        this.endTime = null;
        this.latencies = [];
        this.errors = 0;
        
        this.metricsFile = path.join(__dirname, '../shared-data/research-results', 
            `consumer_research_${this.testId}_worker_${this.workerId}.csv`);
    }

    async initialize() {
        console.log(`[Consumer-${this.workerId}] Initializing research consumer worker`);
        console.log(`[Consumer-${this.workerId}] Assigned Core: ${this.assignedCore}`);
        console.log(`[Consumer-${this.workerId}] Target Messages: ${this.targetMessages}`);
        
        // Verify CPU affinity if available
        await this.verifyCPUAffinity();
        
        // Setup PostgreSQL connection
        this.client = new Client({
            host: process.env.PGMQ_HOST || 'localhost',
            port: process.env.PGMQ_PORT || 5432,
            database: process.env.PGMQ_DATABASE || 'postgres',
            user: process.env.PGMQ_USER || 'postgres',
            password: process.env.PGMQ_PASSWORD || 'password'
        });

        await this.client.connect();
        console.log(`[Consumer-${this.workerId}] Connected to PostgreSQL`);

        // Ensure PGMQ extension
        await this.client.query('CREATE EXTENSION IF NOT EXISTS pgmq;');
        
        // Create metrics file
        await this.initializeMetricsFile();
    }

    async verifyCPUAffinity() {
        if (os.platform() === 'linux') {
            try {
                const { exec } = require('child_process');
                const { promisify } = require('util');
                const execAsync = promisify(exec);
                
                const { stdout } = await execAsync(`taskset -cp ${process.pid}`);
                console.log(`[Consumer-${this.workerId}] CPU Affinity: ${stdout.trim()}`);
            } catch (error) {
                console.log(`[Consumer-${this.workerId}] Could not verify CPU affinity: ${error.message}`);
            }
        }
    }

    async initializeMetricsFile() {
        const dir = path.dirname(this.metricsFile);
        await fs.mkdir(dir, { recursive: true });
        
        const header = 'timestamp,worker_id,message_id,latency_ms,queue_depth,cpu_usage,memory_mb,core_id\n';
        await fs.writeFile(this.metricsFile, header);
    }

    async startConsuming() {
        console.log(`[Consumer-${this.workerId}] Starting message consumption`);
        this.startTime = Date.now();
        
        const queueName = `benchmark_queue_${this.testId}`;
        
        while (this.processedMessages < this.targetMessages) {
            try {
                const startConsume = Date.now();
                
                // Read message from queue
                const result = await this.client.query(
                    'SELECT * FROM pgmq.read($1, 30, 1)',
                    [queueName]
                );

                if (result.rows.length > 0) {
                    const message = result.rows[0];
                    const consumeTime = Date.now();
                    
                    // Parse message to get send timestamp
                    let messageData;
                    try {
                        messageData = JSON.parse(message.message);
                    } catch (e) {
                        messageData = { timestamp: startConsume, data: message.message };
                    }
                    
                    // Calculate latency (time from message creation to consumption)
                    const latency = consumeTime - (messageData.timestamp || startConsume);
                    this.latencies.push(latency);
                    
                    // Delete message from queue
                    await this.client.query(
                        'SELECT pgmq.delete($1, $2)',
                        [queueName, message.msg_id]
                    );
                    
                    this.processedMessages++;
                    
                    // Record detailed metrics
                    await this.recordMetrics(message.msg_id, latency);
                    
                    // Send progress update
                    if (this.processedMessages % 100 === 0) {
                        await this.sendProgressUpdate();
                    }
                    
                } else {
                    // No messages available, wait briefly
                    await this.sleep(10);
                }
                
            } catch (error) {
                this.errors++;
                console.error(`[Consumer-${this.workerId}] Error processing message:`, error.message);
                await this.sleep(100);
            }
        }
        
        this.endTime = Date.now();
        await this.sendFinalMetrics();
        console.log(`[Consumer-${this.workerId}] Completed processing ${this.processedMessages} messages`);
    }

    async recordMetrics(messageId, latency) {
        const timestamp = Date.now();
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        // Get queue depth
        const queueName = `benchmark_queue_${this.testId}`;
        let queueDepth = 0;
        try {
            const depthResult = await this.client.query(
                'SELECT COUNT(*) as depth FROM pgmq.q_$1',
                [queueName.replace('benchmark_queue_', '')]
            );
            queueDepth = parseInt(depthResult.rows[0]?.depth || 0);
        } catch (e) {
            // Queue might not exist or be accessible
        }
        
        const metrics = [
            timestamp,
            this.workerId,
            messageId,
            latency.toFixed(2),
            queueDepth,
            ((cpuUsage.user + cpuUsage.system) / 1000).toFixed(2),
            (memUsage.rss / 1024 / 1024).toFixed(2),
            this.assignedCore || 'unknown'
        ].join(',') + '\n';
        
        await fs.appendFile(this.metricsFile, metrics);
    }

    async sendProgressUpdate() {
        const currentTime = Date.now();
        const elapsed = currentTime - this.startTime;
        const throughput = this.processedMessages / (elapsed / 1000);
        const avgLatency = this.latencies.length > 0 ? 
            this.latencies.reduce((sum, l) => sum + l, 0) / this.latencies.length : 0;
        
        const progress = {
            type: 'progress',
            workerId: this.workerId,
            workerType: 'consumer',
            processed: this.processedMessages,
            target: this.targetMessages,
            throughput: throughput.toFixed(2),
            avgLatency: avgLatency.toFixed(2),
            errors: this.errors,
            timestamp: currentTime
        };
        
        if (process.send) {
            process.send({
                type: 'metrics',
                workerType: 'consumer',
                data: progress
            });
        }
        
        console.log(`[Consumer-${this.workerId}] Progress: ${this.processedMessages}/${this.targetMessages} (${throughput.toFixed(2)} msg/sec, ${avgLatency.toFixed(2)}ms avg latency)`);
    }

    async sendFinalMetrics() {
        const duration = this.endTime - this.startTime;
        const throughput = this.processedMessages / (duration / 1000);
        const avgLatency = this.latencies.length > 0 ? 
            this.latencies.reduce((sum, l) => sum + l, 0) / this.latencies.length : 0;
        
        // Calculate latency statistics
        const sortedLatencies = this.latencies.sort((a, b) => a - b);
        const p50 = sortedLatencies[Math.floor(sortedLatencies.length * 0.5)] || 0;
        const p95 = sortedLatencies[Math.floor(sortedLatencies.length * 0.95)] || 0;
        const p99 = sortedLatencies[Math.floor(sortedLatencies.length * 0.99)] || 0;
        
        const finalMetrics = {
            type: 'final',
            workerId: this.workerId,
            workerType: 'consumer',
            processedMessages: this.processedMessages,
            targetMessages: this.targetMessages,
            duration,
            throughput,
            avgLatency,
            latencyStats: {
                min: Math.min(...this.latencies) || 0,
                max: Math.max(...this.latencies) || 0,
                p50,
                p95,
                p99,
                count: this.latencies.length
            },
            errors: this.errors,
            assignedCore: this.assignedCore,
            timestamp: this.endTime
        };
        
        if (process.send) {
            process.send({
                type: 'metrics',
                workerType: 'consumer',
                data: finalMetrics
            });
        }
        
        console.log(`[Consumer-${this.workerId}] Final: ${throughput.toFixed(2)} msg/sec, ${avgLatency.toFixed(2)}ms avg latency, ${this.errors} errors`);
    }

    async cleanup() {
        if (this.client) {
            await this.client.end();
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Main execution
async function main() {
    const worker = new ResearchConsumerWorker();
    
    try {
        await worker.initialize();
        await worker.startConsuming();
    } catch (error) {
        console.error(`[Consumer-${worker.workerId}] Fatal error:`, error);
        process.exit(1);
    } finally {
        await worker.cleanup();
        process.exit(0);
    }
}

// Handle process signals
process.on('SIGINT', async () => {
    console.log(`[Consumer-${process.env.WORKER_ID}] Received SIGINT, shutting down gracefully`);
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log(`[Consumer-${process.env.WORKER_ID}] Received SIGTERM, shutting down gracefully`);
    process.exit(0);
});

if (require.main === module) {
    main();
}

module.exports = ResearchConsumerWorker;
