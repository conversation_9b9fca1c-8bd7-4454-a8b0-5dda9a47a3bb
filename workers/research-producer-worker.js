const { Client } = require('pg');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

class ResearchProducerWorker {
    constructor() {
        this.workerId = process.env.WORKER_ID || '0';
        this.testId = process.env.TEST_ID || 'research_test';
        this.targetMessages = parseInt(process.env.TARGET_MESSAGES) || 1000;
        this.messageSizeKB = parseInt(process.env.MESSAGE_SIZE_KB) || 1;
        this.assignedCore = process.env.ASSIGNED_CORE;
        
        this.client = null;
        this.sentMessages = 0;
        this.startTime = null;
        this.endTime = null;
        this.sendTimes = [];
        this.errors = 0;
        
        this.metricsFile = path.join(__dirname, '../shared-data/research-results', 
            `producer_research_${this.testId}_worker_${this.workerId}.csv`);
    }

    async initialize() {
        console.log(`[Producer-${this.workerId}] Initializing research producer worker`);
        console.log(`[Producer-${this.workerId}] Assigned Core: ${this.assignedCore}`);
        console.log(`[Producer-${this.workerId}] Target Messages: ${this.targetMessages}`);
        console.log(`[Producer-${this.workerId}] Message Size: ${this.messageSizeKB} KB`);
        
        // Verify CPU affinity if available
        await this.verifyCPUAffinity();
        
        // Setup PostgreSQL connection
        this.client = new Client({
            host: process.env.PGMQ_HOST || 'localhost',
            port: process.env.PGMQ_PORT || 5432,
            database: process.env.PGMQ_DATABASE || 'postgres',
            user: process.env.PGMQ_USER || 'postgres',
            password: process.env.PGMQ_PASSWORD || 'password'
        });

        await this.client.connect();
        console.log(`[Producer-${this.workerId}] Connected to PostgreSQL`);

        // Ensure PGMQ extension
        await this.client.query('CREATE EXTENSION IF NOT EXISTS pgmq;');
        
        // Create queue if it doesn't exist
        const queueName = `benchmark_queue_${this.testId}`;
        try {
            await this.client.query('SELECT pgmq.create($1)', [queueName]);
            console.log(`[Producer-${this.workerId}] Queue created: ${queueName}`);
        } catch (error) {
            if (!error.message.includes('already exists')) {
                throw error;
            }
            console.log(`[Producer-${this.workerId}] Queue exists: ${queueName}`);
        }
        
        // Create metrics file
        await this.initializeMetricsFile();
    }

    async verifyCPUAffinity() {
        if (os.platform() === 'linux') {
            try {
                const { exec } = require('child_process');
                const { promisify } = require('util');
                const execAsync = promisify(exec);
                
                const { stdout } = await execAsync(`taskset -cp ${process.pid}`);
                console.log(`[Producer-${this.workerId}] CPU Affinity: ${stdout.trim()}`);
            } catch (error) {
                console.log(`[Producer-${this.workerId}] Could not verify CPU affinity: ${error.message}`);
            }
        }
    }

    async initializeMetricsFile() {
        const dir = path.dirname(this.metricsFile);
        await fs.mkdir(dir, { recursive: true });
        
        const header = 'timestamp,worker_id,message_id,send_time_ms,message_size_bytes,cpu_usage,memory_mb,core_id\n';
        await fs.writeFile(this.metricsFile, header);
    }

    generateMessage() {
        const timestamp = Date.now();
        const baseMessage = {
            id: `${this.workerId}-${this.sentMessages}`,
            timestamp,
            workerId: this.workerId,
            testId: this.testId,
            sequenceNumber: this.sentMessages
        };
        
        // Pad message to reach target size
        const baseSize = JSON.stringify(baseMessage).length;
        const targetBytes = this.messageSizeKB * 1024;
        const paddingSize = Math.max(0, targetBytes - baseSize - 20); // 20 bytes buffer
        
        if (paddingSize > 0) {
            baseMessage.padding = 'x'.repeat(paddingSize);
        }
        
        return JSON.stringify(baseMessage);
    }

    async startProducing() {
        console.log(`[Producer-${this.workerId}] Starting message production`);
        this.startTime = Date.now();
        
        const queueName = `benchmark_queue_${this.testId}`;
        
        for (let i = 0; i < this.targetMessages; i++) {
            try {
                const sendStart = Date.now();
                const message = this.generateMessage();
                
                // Send message to queue
                await this.client.query(
                    'SELECT pgmq.send($1, $2)',
                    [queueName, message]
                );
                
                const sendEnd = Date.now();
                const sendTime = sendEnd - sendStart;
                this.sendTimes.push(sendTime);
                
                this.sentMessages++;
                
                // Record detailed metrics
                await this.recordMetrics(i, sendTime, message.length);
                
                // Send progress update
                if (this.sentMessages % 100 === 0) {
                    await this.sendProgressUpdate();
                }
                
                // Small delay to prevent overwhelming the system
                if (i < this.targetMessages - 1) {
                    await this.sleep(1);
                }
                
            } catch (error) {
                this.errors++;
                console.error(`[Producer-${this.workerId}] Error sending message ${i}:`, error.message);
                await this.sleep(100);
            }
        }
        
        this.endTime = Date.now();
        await this.sendFinalMetrics();
        console.log(`[Producer-${this.workerId}] Completed sending ${this.sentMessages} messages`);
    }

    async recordMetrics(messageId, sendTime, messageSize) {
        const timestamp = Date.now();
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        const metrics = [
            timestamp,
            this.workerId,
            messageId,
            sendTime.toFixed(2),
            messageSize,
            ((cpuUsage.user + cpuUsage.system) / 1000).toFixed(2),
            (memUsage.rss / 1024 / 1024).toFixed(2),
            this.assignedCore || 'unknown'
        ].join(',') + '\n';
        
        await fs.appendFile(this.metricsFile, metrics);
    }

    async sendProgressUpdate() {
        const currentTime = Date.now();
        const elapsed = currentTime - this.startTime;
        const throughput = this.sentMessages / (elapsed / 1000);
        const avgSendTime = this.sendTimes.length > 0 ? 
            this.sendTimes.reduce((sum, t) => sum + t, 0) / this.sendTimes.length : 0;
        
        const progress = {
            type: 'progress',
            workerId: this.workerId,
            workerType: 'producer',
            sent: this.sentMessages,
            target: this.targetMessages,
            throughput: throughput.toFixed(2),
            avgSendTime: avgSendTime.toFixed(2),
            errors: this.errors,
            timestamp: currentTime
        };
        
        if (process.send) {
            process.send({
                type: 'metrics',
                workerType: 'producer',
                data: progress
            });
        }
        
        console.log(`[Producer-${this.workerId}] Progress: ${this.sentMessages}/${this.targetMessages} (${throughput.toFixed(2)} msg/sec, ${avgSendTime.toFixed(2)}ms avg send time)`);
    }

    async sendFinalMetrics() {
        const duration = this.endTime - this.startTime;
        const throughput = this.sentMessages / (duration / 1000);
        const avgSendTime = this.sendTimes.length > 0 ? 
            this.sendTimes.reduce((sum, t) => sum + t, 0) / this.sendTimes.length : 0;
        
        // Calculate send time statistics
        const sortedSendTimes = this.sendTimes.sort((a, b) => a - b);
        const p50 = sortedSendTimes[Math.floor(sortedSendTimes.length * 0.5)] || 0;
        const p95 = sortedSendTimes[Math.floor(sortedSendTimes.length * 0.95)] || 0;
        const p99 = sortedSendTimes[Math.floor(sortedSendTimes.length * 0.99)] || 0;
        
        const finalMetrics = {
            type: 'final',
            workerId: this.workerId,
            workerType: 'producer',
            sentMessages: this.sentMessages,
            targetMessages: this.targetMessages,
            duration,
            throughput,
            avgSendTime,
            sendTimeStats: {
                min: Math.min(...this.sendTimes) || 0,
                max: Math.max(...this.sendTimes) || 0,
                p50,
                p95,
                p99,
                count: this.sendTimes.length
            },
            errors: this.errors,
            assignedCore: this.assignedCore,
            timestamp: this.endTime
        };
        
        if (process.send) {
            process.send({
                type: 'metrics',
                workerType: 'producer',
                data: finalMetrics
            });
        }
        
        console.log(`[Producer-${this.workerId}] Final: ${throughput.toFixed(2)} msg/sec, ${avgSendTime.toFixed(2)}ms avg send time, ${this.errors} errors`);
    }

    async cleanup() {
        if (this.client) {
            await this.client.end();
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Main execution
async function main() {
    const worker = new ResearchProducerWorker();
    
    try {
        await worker.initialize();
        await worker.startProducing();
    } catch (error) {
        console.error(`[Producer-${worker.workerId}] Fatal error:`, error);
        process.exit(1);
    } finally {
        await worker.cleanup();
        process.exit(0);
    }
}

// Handle process signals
process.on('SIGINT', async () => {
    console.log(`[Producer-${process.env.WORKER_ID}] Received SIGINT, shutting down gracefully`);
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log(`[Producer-${process.env.WORKER_ID}] Received SIGTERM, shutting down gracefully`);
    process.exit(0);
});

if (require.main === module) {
    main();
}

module.exports = ResearchProducerWorker;
