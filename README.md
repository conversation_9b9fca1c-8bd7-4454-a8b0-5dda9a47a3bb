# PGMQ Benchmark System

Comprehensive PostgreSQL Message Queue (PGMQ) performance benchmarking system with Node.js cluster mode, shared CSV data management, and real-time web dashboard for throughput visualization.

## Features

### 🚀 **Core Improvements (Latest Update)**
- **FIXED: Real-time Latency Measurement**: Producers and consumers now run concurrently for accurate latency metrics
- **Dynamic Worker Configuration**: Runtime adjustment of producer/consumer ratios with CPU core allocation
- **Queue Depth Monitoring**: Real-time tracking of message backlog and flow patterns
- **Enhanced Dashboard**: Historical data persistence, queue visualization, and export functionality

### 📊 **Benchmarking Capabilities**
- **Node.js Cluster Mode**: Enhanced producer and consumer with PM2 cluster management
- **Shared Data Management**: Centralized CSV data storage in `shared-data/` directory
- **Real-time Web Dashboard**: Interactive throughput visualization with Chart.js
- **Stress Testing**: Automated testing to find PostgreSQL PGMQ performance limits
- **Resource Monitoring**: CPU, memory, and system metrics collection
- **Configurable Testing**: Adjustable message volumes, sizes, and worker instances

## System Requirements

- Node.js 16+ 
- PostgreSQL 15+ with PGMQ extension
- PM2 (for cluster management)
- 4GB+ RAM recommended for stress testing

## Installation

1. **Install dependencies:**
```bash
npm install
```

2. **Install PM2 globally:**
```bash
npm install -g pm2
```

3. **Setup PostgreSQL with PGMQ:**
```sql
CREATE EXTENSION IF NOT EXISTS pgmq;
CREATE SCHEMA IF NOT EXISTS pgmq;
```

## Configuration

Edit `config/benchmark-config.js` to customize:

- **Database settings**: Host, port, credentials
- **Test parameters**: Message count, size, worker instances
- **Stress test limits**: Max messages, sizes, timeouts
- **Dashboard settings**: Port, update intervals

## Usage

### 1. Basic Benchmark

Run a single benchmark test:
```bash
npm run benchmark
```

### 2. Stress Testing

Find PostgreSQL PGMQ performance limits:
```bash
npm run stress-test
```

This will:
- Test increasing message volumes (1K → 100K)
- Test different message sizes (1KB → 500KB)
- Test various concurrency configurations
- Generate comprehensive performance report

### 3. Web Dashboard

Start the real-time dashboard:
```bash
npm run dashboard
```

Access at: `http://localhost:3000`

Features:
- Real-time throughput monitoring
- Latency distribution charts
- Historical performance trends
- System resource utilization

### 4. Test Concurrent Improvements

Test the new concurrent benchmarking system:

```bash
npm run test-concurrent
```

This comprehensive test demonstrates:
- Real-time latency measurement accuracy
- Dynamic worker configuration optimization
- Queue depth monitoring capabilities
- Performance comparison across different scenarios

### 5. Manual Cluster Mode

Start producer and consumer clusters manually:

```bash
# Start producers (2 instances)
npm run producer

# Start consumers (2 instances)
npm run consumer

# Monitor with PM2
pm2 status
pm2 logs
```

## Data Output

### Shared Data Structure
```
shared-data/
├── results/           # CSV benchmark results
│   ├── producer_metrics_*.csv
│   ├── consumer_metrics_*.csv
│   └── benchmark_results_*.csv
└── logs/             # Application logs
    ├── benchmark_*.log
    ├── producer-*.log
    └── consumer-*.log
```

### CSV Data Format

**Producer Metrics:**
```csv
test_id,message_id,size_kb,sent_timestamp,worker_id
```

**Consumer Metrics:**
```csv
test_id,message_id,size_kb,sent_timestamp,received_timestamp,latency_ms,worker_id
```

**Benchmark Results:**
```csv
test_id,timestamp,total_messages,message_size_kb,producer_instances,consumer_instances,producer_throughput,consumer_throughput,avg_latency_ms,cpu_usage,memory_usage,status
```

## Environment Variables

Key configuration options:

```bash
# Database
DB_HOST=localhost
DB_PORT=5433
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=postgres

# Test Parameters
TOTAL_MESSAGES=10000
MESSAGE_SIZE_KB=1
PRODUCER_INSTANCES=2
CONSUMER_INSTANCES=2

# Dashboard
DASHBOARD_PORT=3000

# PostgreSQL Version
PG_VERSION=15
PG_DESCRIPTION="PostgreSQL 15 Stable"
```

## Performance Testing Scenarios

### 1. Throughput Testing
- **Objective**: Find maximum messages/second
- **Method**: Increase message volume until performance degrades
- **Metrics**: Producer/Consumer throughput, latency

### 2. Message Size Testing  
- **Objective**: Test with different payload sizes
- **Method**: Test 1KB, 10KB, 50KB, 100KB, 500KB messages
- **Metrics**: Throughput vs message size relationship

### 3. Concurrency Testing
- **Objective**: Optimal producer/consumer ratio
- **Method**: Test 1-8 producers with 1-8 consumers
- **Metrics**: Best throughput configuration

### 4. Resource Limit Testing
- **Objective**: Find system resource constraints
- **Method**: Monitor CPU/memory during high load
- **Metrics**: Resource utilization vs performance

## Expected Performance Baselines

For PostgreSQL 15 on modern hardware:

- **Small Messages (1KB)**: 1,000-5,000 msg/sec
- **Medium Messages (10KB)**: 500-2,000 msg/sec  
- **Large Messages (100KB)**: 100-500 msg/sec
- **Latency**: 1-50ms typical range

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify PostgreSQL is running
   - Check PGMQ extension is installed
   - Confirm connection parameters

2. **Performance Issues**
   - Increase PostgreSQL connection limits
   - Tune `shared_buffers` and `work_mem`
   - Monitor disk I/O and network

3. **Memory Issues**
   - Reduce message size or volume
   - Increase Node.js heap size: `--max-old-space-size=4096`
   - Monitor with `pm2 monit`

### Logs and Debugging

```bash
# View PM2 logs
pm2 logs

# View benchmark logs
tail -f shared-data/logs/benchmark_*.log

# Monitor system resources
pm2 monit
```

## Research Applications

This benchmarking system is designed for:

- **PostgreSQL Version Comparisons**: Test different PG versions
- **Configuration Optimization**: Find optimal settings
- **Hardware Scaling**: Test different server configurations  
- **Load Testing**: Simulate production workloads
- **Performance Regression**: Track performance over time

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request

## License

MIT License - see LICENSE file for details.
