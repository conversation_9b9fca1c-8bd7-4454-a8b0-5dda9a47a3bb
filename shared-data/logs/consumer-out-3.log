2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Menyiapkan ekstensi PGMQ...
2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Menyimpan data ke: /home/<USER>/code/new_kuliah/shared-data/results/consumer_metrics_manual_test.csv
2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Target: 10000 messages
2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Connecting to PostgreSQL...
2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Starting message consumption...
2025-05-31T23:40:24: [2025-05-31 16:40:24] [Consumer-pm2] 1000 messages processed (238.21 msg/sec, avg latency: 6043.85ms)
2025-05-31T23:40:28: [2025-05-31 16:40:28] [Consumer-pm2] 2000 messages processed (237.25 msg/sec, avg latency: 6910.83ms)
2025-05-31T23:40:32: [2025-05-31 16:40:32] [Consumer-pm2] 3000 messages processed (233.55 msg/sec, avg latency: 7959.15ms)
2025-05-31T23:40:37: [2025-05-31 16:40:37] [Consumer-pm2] 4000 messages processed (231.94 msg/sec, avg latency: 9101.18ms)
2025-05-31T23:40:41: [2025-05-31 16:40:41] [Consumer-pm2] 5000 messages processed (229.98 msg/sec, avg latency: 10302.24ms)
2025-05-31T23:40:45: [2025-05-31 16:40:45] [Consumer-pm2] 6000 messages processed (230.24 msg/sec, avg latency: 11465.66ms)
2025-05-31T23:40:50: [2025-05-31 16:40:50] [Consumer-pm2] 7000 messages processed (229.19 msg/sec, avg latency: 12620.99ms)
2025-05-31T23:40:54: [2025-05-31 16:40:54] [Consumer-pm2] 8000 messages processed (228.53 msg/sec, avg latency: 13767.72ms)
