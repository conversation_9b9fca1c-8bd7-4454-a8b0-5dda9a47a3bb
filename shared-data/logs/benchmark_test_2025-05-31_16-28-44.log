[2025-05-31 16:28:44] 
=== System Information ===
[2025-05-31 16:28:44] CPU: AMD Ryzen 7 6800H with Radeon Graphics (16 cores)
[2025-05-31 16:28:44] Memory: 7 GB
[2025-05-31 16:28:44] OS: Ubuntu 24.04.2 LTS
[2025-05-31 16:28:44] PostgreSQL: PostgreSQL 15 Stable
[2025-05-31 16:28:44] Test ID: test_2025-05-31_16-28-44
[2025-05-31 16:28:44] 
=== Starting Concurrent Benchmark ===
[2025-05-31 16:28:44] Messages: 10000, Size: 1KB
[2025-05-31 16:28:44] Producers: 2, Consumers: 2
[2025-05-31 16:28:44] Starting queue depth monitoring...
[2025-05-31 16:28:44] Starting consumers...
[2025-05-31 16:28:46] Starting producers concurrently...
[2025-05-31 16:28:58] All producers completed
[2025-05-31 16:29:10] All consumers completed
[2025-05-31 16:29:10] 
=== Concurrent Benchmark Results ===
[2025-05-31 16:29:10] Producer Throughput: 855.58 msg/sec
[2025-05-31 16:29:10] Consumer Throughput: 394.60 msg/sec
[2025-05-31 16:29:10] Average Latency: 6005.27 ms (REAL-TIME)
[2025-05-31 16:29:10] Max Queue Depth: 0 messages
[2025-05-31 16:29:10] Avg Queue Depth: 0.00 messages
[2025-05-31 16:29:10] CPU Usage: 4.71%
[2025-05-31 16:29:10] Memory Usage: 82.49%
[2025-05-31 16:29:10] Status: SUCCESS
[2025-05-31 16:29:10] 
=== Benchmark Complete ===
