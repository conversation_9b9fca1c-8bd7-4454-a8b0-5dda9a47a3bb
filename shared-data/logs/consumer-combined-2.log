2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Menyiapkan ekstensi PGMQ...
2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Menyimpan data ke: /home/<USER>/code/new_kuliah/shared-data/results/consumer_metrics_manual_test.csv
2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Target: 10000 messages
2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Connecting to PostgreSQL...
2025-05-31T23:40:19: [2025-05-31 16:40:19] [Consumer-pm2] Starting message consumption...
2025-05-31T23:40:24: [2025-05-31 16:40:24] [Consumer-pm2] 1000 messages processed (240.33 msg/sec, avg latency: 6023.95ms)
2025-05-31T23:40:28: [2025-05-31 16:40:28] [Consumer-pm2] 2000 messages processed (238.15 msg/sec, avg latency: 6885.10ms)
2025-05-31T23:40:32: [2025-05-31 16:40:32] [Consumer-pm2] 3000 messages processed (233.95 msg/sec, avg latency: 7930.27ms)
2025-05-31T23:40:37: [2025-05-31 16:40:37] [Consumer-pm2] 4000 messages processed (232.37 msg/sec, avg latency: 9070.57ms)
2025-05-31T23:40:41: [2025-05-31 16:40:41] [Consumer-pm2] 5000 messages processed (230.10 msg/sec, avg latency: 10271.47ms)
2025-05-31T23:40:45: [2025-05-31 16:40:45] [Consumer-pm2] 6000 messages processed (230.41 msg/sec, avg latency: 11435.64ms)
2025-05-31T23:40:50: [2025-05-31 16:40:50] [Consumer-pm2] 7000 messages processed (229.24 msg/sec, avg latency: 12591.36ms)
2025-05-31T23:40:54: [2025-05-31 16:40:54] [Consumer-pm2] 8000 messages processed (228.62 msg/sec, avg latency: 13738.64ms)
