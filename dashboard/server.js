// Express.js server for PGMQ Benchmark Dashboard
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const config = require('../config/benchmark-config');
const StatisticalAnalysis = require('../lib/statistical-analysis');

class DashboardServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = new WebSocket.Server({ server: this.server });
    this.clients = new Set();
    this.statisticalAnalysis = new StatisticalAnalysis();

    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
  }

  setupMiddleware() {
    this.app.use(express.static(path.join(__dirname, 'public')));
    this.app.use(express.json());
  }

  setupRoutes() {
    // Serve main dashboard
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });

    // API to get benchmark results
    this.app.get('/api/results', async (req, res) => {
      try {
        const results = await this.getBenchmarkResults();
        res.json(results);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API to get latest test data
    this.app.get('/api/latest', async (req, res) => {
      try {
        const latest = await this.getLatestTestData();
        res.json(latest);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API to get system info
    this.app.get('/api/system', (req, res) => {
      res.json({
        postgresql: config.postgresql.description,
        version: config.postgresql.version,
        timestamp: new Date().toISOString()
      });
    });

    // API to get throughput history
    this.app.get('/api/throughput-history', async (req, res) => {
      try {
        const limit = parseInt(req.query.limit) || 50;
        const history = await this.getThroughputHistory(limit);
        res.json(history);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API to get queue depth history
    this.app.get('/api/queue-history', async (req, res) => {
      try {
        const testId = req.query.testId;
        const history = await this.getQueueHistory(testId);
        res.json(history);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API to compare test results
    this.app.get('/api/compare', async (req, res) => {
      try {
        const testIds = req.query.testIds ? req.query.testIds.split(',') : [];
        const comparison = await this.compareTestResults(testIds);
        res.json(comparison);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API to export historical data
    this.app.get('/api/export', async (req, res) => {
      try {
        const format = req.query.format || 'json';
        const data = await this.exportHistoricalData(format);

        if (format === 'csv') {
          res.setHeader('Content-Type', 'text/csv');
          res.setHeader('Content-Disposition', 'attachment; filename=benchmark_export.csv');
        } else {
          res.setHeader('Content-Type', 'application/json');
          res.setHeader('Content-Disposition', 'attachment; filename=benchmark_export.json');
        }

        res.send(data);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API for statistical analysis
    this.app.get('/api/statistical-analysis', async (req, res) => {
      try {
        const testIds = req.query.testIds ? req.query.testIds.split(',') : null;
        const analysis = await this.statisticalAnalysis.analyzeResearchResults(testIds);
        res.json(analysis);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API to generate publication report
    this.app.post('/api/publication-report', async (req, res) => {
      try {
        const testIds = req.body.testIds || null;
        const report = await this.statisticalAnalysis.generatePublicationReport(testIds);
        res.json(report);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API to compare datasets statistically
    this.app.post('/api/statistical-comparison', async (req, res) => {
      try {
        const { dataset1, dataset2, name1, name2 } = req.body;
        const comparison = this.statisticalAnalysis.compareDatasets(dataset1, dataset2, name1, name2);
        res.json(comparison);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // API to get research results
    this.app.get('/api/research-results', async (req, res) => {
      try {
        const resultsDir = path.join(__dirname, '../shared-data/research-results');
        const files = fs.readdirSync(resultsDir)
          .filter(file => file.startsWith('research_') && file.endsWith('.json'))
          .sort()
          .reverse();

        const results = [];
        for (const file of files.slice(0, 20)) { // Last 20 research results
          const filePath = path.join(resultsDir, file);
          const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
          results.push({
            filename: file,
            testId: data.configuration?.testId,
            timestamp: data.metadata?.timestamp,
            configuration: data.configuration,
            summary: data.results?.performance
          });
        }

        res.json(results);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws) => {
      console.log('Client connected to dashboard');
      this.clients.add(ws);

      ws.on('close', () => {
        console.log('Client disconnected from dashboard');
        this.clients.delete(ws);
      });

      // Send initial data
      this.sendLatestData(ws);
    });
  }

  async getBenchmarkResults() {
    const resultsDir = config.data.resultsDir;
    const files = fs.readdirSync(resultsDir)
      .filter(file => file.startsWith('benchmark_results_') && file.endsWith('.csv'))
      .sort()
      .reverse(); // Most recent first

    const allResults = [];

    for (const file of files.slice(0, 10)) { // Last 10 test files
      const filePath = path.join(resultsDir, file);
      const results = await this.readCsvFile(filePath);
      allResults.push(...results);
    }

    return allResults.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  async getLatestTestData() {
    const results = await this.getBenchmarkResults();
    if (results.length === 0) return null;

    const latest = results[0];
    
    // Get detailed metrics for the latest test
    const testId = latest.test_id;
    const producerFile = path.join(config.data.resultsDir, `producer_metrics_${testId}.csv`);
    const consumerFile = path.join(config.data.resultsDir, `consumer_metrics_${testId}.csv`);

    let producerMetrics = [];
    let consumerMetrics = [];

    if (fs.existsSync(producerFile)) {
      producerMetrics = await this.readCsvFile(producerFile);
    }

    if (fs.existsSync(consumerFile)) {
      consumerMetrics = await this.readCsvFile(consumerFile);
    }

    return {
      summary: latest,
      producerMetrics: producerMetrics.slice(0, 100), // Limit for performance
      consumerMetrics: consumerMetrics.slice(0, 100)
    };
  }

  async getThroughputHistory(limit = 50) {
    const results = await this.getBenchmarkResults();

    return results.slice(0, limit).map(result => ({
      testId: result.test_id,
      timestamp: new Date(parseInt(result.timestamp)).toISOString(),
      producerThroughput: parseFloat(result.producer_throughput),
      consumerThroughput: parseFloat(result.consumer_throughput),
      avgLatency: parseFloat(result.avg_latency_ms),
      maxQueueDepth: parseInt(result.max_queue_depth || 0),
      avgQueueDepth: parseFloat(result.avg_queue_depth || 0),
      messageSize: parseInt(result.message_size_kb),
      totalMessages: parseInt(result.total_messages),
      producerInstances: parseInt(result.producer_instances),
      consumerInstances: parseInt(result.consumer_instances),
      status: result.status
    }));
  }

  async getQueueHistory(testId) {
    if (!testId) return [];

    const queueFile = path.join(config.data.resultsDir, `queue_metrics_${testId}.csv`);
    if (!fs.existsSync(queueFile)) return [];

    const queueData = await this.readCsvFile(queueFile);
    return queueData.map(row => ({
      timestamp: new Date(parseInt(row.timestamp)).toISOString(),
      queueDepth: parseInt(row.queue_depth),
      testId: row.test_id
    }));
  }

  async compareTestResults(testIds) {
    if (testIds.length === 0) return { error: 'No test IDs provided' };

    const comparisons = [];

    for (const testId of testIds) {
      const results = await this.getBenchmarkResults();
      const testResult = results.find(r => r.test_id === testId);

      if (testResult) {
        const queueHistory = await this.getQueueHistory(testId);

        comparisons.push({
          testId,
          timestamp: new Date(parseInt(testResult.timestamp)).toISOString(),
          metrics: {
            producerThroughput: parseFloat(testResult.producer_throughput),
            consumerThroughput: parseFloat(testResult.consumer_throughput),
            avgLatency: parseFloat(testResult.avg_latency_ms),
            maxQueueDepth: parseInt(testResult.max_queue_depth || 0),
            avgQueueDepth: parseFloat(testResult.avg_queue_depth || 0)
          },
          config: {
            totalMessages: parseInt(testResult.total_messages),
            messageSize: parseInt(testResult.message_size_kb),
            producers: parseInt(testResult.producer_instances),
            consumers: parseInt(testResult.consumer_instances)
          },
          queueHistory: queueHistory.slice(0, 100) // Limit for performance
        });
      }
    }

    return {
      comparisons,
      summary: this.generateComparisonSummary(comparisons)
    };
  }

  generateComparisonSummary(comparisons) {
    if (comparisons.length === 0) return {};

    const throughputs = comparisons.map(c => c.metrics.consumerThroughput);
    const latencies = comparisons.map(c => c.metrics.avgLatency);

    return {
      bestThroughput: {
        testId: comparisons[throughputs.indexOf(Math.max(...throughputs))].testId,
        value: Math.max(...throughputs)
      },
      bestLatency: {
        testId: comparisons[latencies.indexOf(Math.min(...latencies))].testId,
        value: Math.min(...latencies)
      },
      avgThroughput: throughputs.reduce((a, b) => a + b, 0) / throughputs.length,
      avgLatency: latencies.reduce((a, b) => a + b, 0) / latencies.length
    };
  }

  async exportHistoricalData(format = 'json') {
    const results = await this.getBenchmarkResults();

    if (format === 'csv') {
      const headers = Object.keys(results[0] || {}).join(',');
      const rows = results.map(result =>
        Object.values(result).map(val =>
          typeof val === 'string' && val.includes(',') ? `"${val}"` : val
        ).join(',')
      );
      return [headers, ...rows].join('\n');
    }

    return JSON.stringify({
      exportDate: new Date().toISOString(),
      totalTests: results.length,
      data: results
    }, null, 2);
  }

  async readCsvFile(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      
      if (!fs.existsSync(filePath)) {
        resolve([]);
        return;
      }

      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', reject);
    });
  }

  async sendLatestData(ws = null) {
    try {
      const latest = await this.getLatestTestData();
      const message = JSON.stringify({
        type: 'latest_data',
        data: latest
      });

      if (ws) {
        ws.send(message);
      } else {
        this.clients.forEach(client => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(message);
          }
        });
      }
    } catch (error) {
      console.error('Error sending latest data:', error.message);
    }
  }

  startMonitoring() {
    // Send updates every 5 seconds
    setInterval(() => {
      this.sendLatestData();
    }, 5000);
  }

  start(port = config.dashboard.port) {
    this.server.listen(port, () => {
      console.log(`Dashboard server running on http://localhost:${port}`);
      this.startMonitoring();
    });
  }
}

// Start server if called directly
if (require.main === module) {
  const dashboard = new DashboardServer();
  dashboard.start();
}

module.exports = DashboardServer;
